<script setup lang="ts">
import { ref, provide } from 'vue'
import Analysis from '../analysis.vue'

const activeName = ref('analysis')

provide('activeName', activeName)
</script>

<template>
  <div id="app">
    <Analysis />
  </div>
</template>

<style>
/* 导入原始CSS文件 */
@import url('../003/css/style.css');

#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
