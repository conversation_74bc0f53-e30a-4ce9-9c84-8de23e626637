<script setup lang="ts">

  import { ref, reactive,getCurrentInstance,onMounted,nextTick } from 'vue'
  import http from "@/axios/http";
  import { useI18n } from '@/hooks/web/useI18n'
  import {pageFormType} from "@/components/public/types"
  import {table_null_lin} from "@/components/public/model.ts"
  import {formType,productFormType,FormRules} from "./model/types"
  import {formList,columns,productFormList,logServerFormList} from "./model/index"
  import {confirmBox} from '@/components/public/elMessageBox'


  const { t } = useI18n()
  const loading = ref(false)


  //调接口使用方法
  const {
    appContext: {
      config: { globalProperties }
    },
    proxy
  } = getCurrentInstance();


  const form = reactive(<formType>{
    status: ''
  })


  // 定义添加或编辑变量
  let addorEdit = ref(1);
  let defaultOrChg = ref(1);
  
  const logServerFormRef = ref();


  const productForm = reactive(<productFormType>{
    name: '',
    log_type:"告警日志",
    manufacturer:"",
    platform_type:"",
    platform_name:"",
    ip:"",
    detail:"",
    status:"enabled"
  })



  const logServerForm = reactive({
    enabled: true,
    port:0,
    protocol:""
  });


  // 分页参数
  const logServerPagination = reactive({
    current_page: 1,
    page_size: 10,
    total: 0
  });
    // 日志服务器配置列表
  const logServerList = ref([]);


  

  // 日志服务器表单配置

    

  // 日志服务器表单验证规则
const logServerRules = ref(<FormRules>{
  port: [
    { required: true, message: t("form.portRequired"), trigger: "blur" },
    { type: 'number', min: 1, max: 65535, message: t("form.portRange"), trigger: 'blur' }
  ],
  protocol: [
    { required: true, message: t("form.protocolRequired"), trigger: "blur" }
  ]
});


// 获取日志服务器配置列表
const getLogServerList = () => {
  const params = {
    current_page: logServerPagination.current_page,
    page_size: logServerPagination.page_size
  };
  
  http.post(globalProperties.$url.syslogServer + '/list', JSON.stringify(params))
    .then(res => {
      if (res && res.records) {
        logServerList.value = res.records || [];
        // 如果返回了分页信息，更新总数
        if (res.total_size !== 0) {
          logServerPagination.total = res.total_size;
        }
      } else {
        logServerList.value = [];
      }
    })
    .catch(err => {
      proxy.$message.error("获取日志服务器配置失败");
      console.error("获取日志服务器配置失败:", err);
      logServerList.value = [];
    });
    
};

// 处理分页变化
const handleLogServerPageChange = (page) => {
  logServerPagination.current_page = page;
  getLogServerList();
};

// 处理每页条数变化
const handleLogServerSizeChange = (size) => {
  logServerPagination.page_size = size;
  logServerPagination.current_page = 1; // 重置到第一页
  getLogServerList();
};


// 打开日志服务器配置弹窗
const logServerConfig = () => {
  logServerVisible.value = true;
  getLogServerList();
};

// 添加日志服务器配置
const addLogServer = () => {
  // 重置表单为默认值
  logServerForm.enabled = true;
  logServerForm.port = 0;
  logServerForm.protocol = "TCP";
  
  // 打开添加模式的弹窗
  logServerVisible.value = true;
  isEditLogServer.value = false;
};

// 编辑状态标记
const isEditLogServer = ref(false);
const currentLogServerId = ref(null);

// 编辑日志服务器配置
const editLogServer = (item) => {
  isEditLogServer.value = true;
  currentLogServerId.value = item.id;
  
  // 填充表单数据
  logServerForm.enabled = item.enabled;
  logServerForm.port = item.port;
  logServerForm.protocol = item.protocol;
  
  // 打开编辑模式的弹窗
  logServerVisible.value = true;
};

// 删除日志服务器配置
const deleteLogServer = (id) => {
  confirmBox(t('form.confirmDelete'), t('common.ok'), null).then(() => {
    http.delete(globalProperties.$url.syslogServer + "/"+id).then(() => {
      proxy.$message.success(t('form.deleteSuccess'));
      getLogServerList();
    }).catch(err => {
      proxy.$message.error(t('form.deleteFailed'));
    });
  });
};


// 关闭日志服务器配置弹窗
const handleLogServerClose = () => {
  logServerVisible.value = false;
};

// 取消日志服务器配置
const handleLogServerCancel = () => {
  logServerVisible.value = false;
};




// 提交日志服务器配置
const handleLogServerConfirm = () => {
  logServerFormRef.value.form.validate(valid => {
    if (valid) {
      if (isEditLogServer.value) {
        // 更新配置
        http.put(globalProperties.$url.syslogServer + "/"+currentLogServerId.value, 
          JSON.stringify(logServerForm)
        ).then(() => {
          proxy.$message.success(t('form.updateSuccess'));
          logServerVisible.value = false;
          getLogServerList();
        }).catch(err => {
          proxy.$message.error(t('form.updateFailed'));
        });
      } else {
        // 添加配置
        http.post(globalProperties.$url.syslogServer, 
          JSON.stringify(logServerForm)
        ).then(() => {
          proxy.$message.success(t('form.addSuccess'));
          logServerVisible.value = false;
          getLogServerList();
        }).catch(err => {
          proxy.$message.error(t('form.addFailed'));
        });
      }
    }
  });
};





  let formRules = ref(<FormRules>{
    name: [{ required: true, message: "请输入联动标识", trigger: "blur" }],
    manufacturer: [{ required: true, message: "请选择厂商", trigger: "blur" }],
    platform_type: [{ required: true, message: "请选择系统类型", trigger: "blur" }],
    platform_name: [{ required: true, message: "请选择系统名称", trigger: "blur" }],
    ip: [{ required: true, message: "请输入IP地址", trigger: "blur" },
      { pattern: globalProperties.$regular.ipRange, message: 'IP地址输入错误', trigger: 'blur' }]
  });


  let tableData = reactive(<any>[]);
  let operateShow = ref(true)


  const thirdPlatformInfo =()=>{
    http.get(globalProperties.$url.thirdPlatform, {}).then(res=>{
      tableData.length = 0;
      tableData.push(...table_null_lin(res));
    });
  };

  //状态改变
  const serchFormInfo=()=>{
    http.put(globalProperties.$url.syslogServer, JSON.stringify(form)).then(res=>{
      proxy.$message.success('修改状态成功！')
    });
  };

  //获取类型
  //log_type:"",manufacturer:"",platform_type:""
  let formChangeType = ref(1);
  let formChangeParams = reactive(<Object>{});


  const platformJsonInfo  = ()=>{
    formChangeType.value = 1;
    formChangeParams = {log_type:productForm.log_type};
    getTypeList(formChangeParams, formChangeType.value)
  };


  const getTypeList = (params:Object,type:Number)=>{
    http.get(globalProperties.$url.platformJson,params).then(res=>{
      if(res&& res.length>0){
        if(type==1){
          productFormList[2].options.length = [];
          res.forEach((item)=>{
            productFormList[2].options.push({label:item,value:item})
          });
          if(defaultOrChg.value==1){
            productForm.manufacturer = res[0];
          }
          formChangeType.value = 2;
          formChangeParams = {log_type:productForm.log_type,manufacturer: productForm.manufacturer};
          getTypeList(formChangeParams, formChangeType.value)
        }else if(type==2){
          productFormList[3].options.length = [];
          res.forEach((item)=>{
            productFormList[3].options.push({label:item,value:item})
          });
          if(defaultOrChg.value==1){
            productForm.platform_type = res[0];
          }
          formChangeType.value = 3;
          formChangeParams = {log_type:productForm.log_type,manufacturer: productForm.manufacturer,platform_type: productForm.platform_type};
          getTypeList(formChangeParams, formChangeType.value)
        }else if(type==3){
          productFormList[4].options.length = [];
          res.forEach((item)=>{
            productFormList[4].options.push({label:item,value:item})
          });
          if(defaultOrChg.value==1){
            productForm.platform_name = res[0];
          }
        }
      }
    });
  };


  //表单多级联动选择
  const formDataSelect =(field:String)=>{
    if(field.field=='log_type'){
      formChangeType.value = 1;
      formChangeParams = {log_type:productForm.log_type};
    }else if(field.field=='manufacturer'){
      formChangeType.value = 2;
      formChangeParams = {log_type:productForm.log_type,manufacturer: productForm.manufacturer};
    }else if(field.field=='systemType'){
      formChangeType.value = 3;
      formChangeParams = {log_type:productForm.log_type,manufacturer: productForm.manufacturer,platform_type: productForm.platform_type};
    }else{
      return false;
    }
    defaultOrChg.value = 1;
    getTypeList(formChangeParams, formChangeType.value)
  };


  //弹窗
  const dialogVisible = ref(false);
  const modalTitle = ref(t("router.product"));


  const logServerVisible = ref(true);
  const logservermodalTitle = ref(t("router.logserverconfig"));



  const productClick =()=>{
    addorEdit.value = 1;
    defaultOrChg.value = 1;
    dialogVisible.value=true;
    platformJsonInfo();
    nextTick(()=>{
      formRuleRef.value.form.resetFields();
    });
  };




  const handleCancel =()=>{
    dialogVisible.value = false;
  };
  const handleClose =()=>{
    dialogVisible.value = false;
  };
  let formRuleRef=ref();
  const handleConfirm =()=>{
    if(addorEdit.value==1){
      formRuleRef.value.form.validate(valid => {
        if (valid) {
          http.post(globalProperties.$url.thirdPlatform, JSON.stringify(productForm)).then(res=>{
            dialogVisible.value=false;
            thirdPlatformInfo();
          }).catch((res)=>{
            proxy.$message.error("内部错误! 状态码:" + res.response.status);
          });
        }
      });
    }else{
      formRuleRef.value.form.validate(valid => {
        if (valid) {
          http.put(globalProperties.$url.thirdPlatform+'/' + productForm.id, JSON.stringify(productForm)).then(res=>{
            dialogVisible.value=false;
            thirdPlatformInfo();
          }).catch((res)=>{
            proxy.$message.error("内部错误! 状态码:" + res.response.status);
          });
        }
      });
    }
  };


  const deleteEvent =(row:Object)=>{
    confirmBox('删除平台后出将不会接收日志传输!，确认删除吗?','确定',null).then(res => {
      http.delete(globalProperties.$url.thirdPlatform+'/' + row.row.id, {}).then(res=>{
        proxy.$message.success("删除成功！");
        thirdPlatformInfo();
      });
    }).catch(res => {
      console.log("取消关闭：", res)
    })
  };

  const editEvent =(row:Object)=>{
    addorEdit.value = 2;
    defaultOrChg.value = 2;
    dialogVisible.value=true;
    for(let i in row.row){
      productForm[i] = row.row[i];
    }
    productForm.id = row.row.id;
    formChangeType.value = 1;
    formChangeParams = {log_type:productForm.log_type};
    getTypeList(formChangeParams, formChangeType.value)
  };

  onMounted(async () => {
    thirdPlatformInfo()
  })

  const activeNames = ref([]);

  const toggleCollapse = () => {
    if (activeNames.value.length > 0) {
      activeNames.value = [];
    } else {
      activeNames.value = ['1']; // 假设有两个折叠项
    }
  };
</script>

<template>
  <MainSlot :mainTitle="t('router.product')" :loading="loading" :isListShow="true">
    <template #mainTop>
<!--      <p>{{t('form.systemReceives')}}SYSLOG {{t('form.port')}} [ 514 ]   {{t('form.protocol')}} [ TCP/UDP ]</p>-->
      <el-button @click="toggleCollapse" type="primary">检索</el-button>
      <ElButton type="primary" @click="productClick">{{t('router.product')}}</ElButton>
      <ElButton type="primary" @click="logServerConfig">{{t('router.logserverconfig')}}</ElButton>

      <tableSet
              :text="'false'"
              :columns="columns"
      />
      <el-collapse v-model="activeNames" class="collapseSearch">
        <el-collapse-item title="" name="1">
          <Form  :formData="form" :formList="formList" :commitBtnShow="true" @serchFormInfo="serchFormInfo"></Form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #mainList>
      <Tablelist :columns="columns" :tableData="tableData" :operateShow="operateShow" :deleteShow="operateShow" @deleteEvent="deleteEvent" :editShow="operateShow" @editEvent="editEvent">
        <template #manufacturerInfo="{row}">
          <span>{{row.manufacturer}}-{{row.platform_type}}-{{row.platform_name}}</span>
        </template>
        <template #status="{row}">
          <span v-if="row.status=='enabled'"  style="color: #009688;font-size: 14px;"><font-awesome-icon icon="far fa-check-circle" /></span>
          <span v-else style="color: #c1000b;font-size: 14px;"><font-awesome-icon icon="far fa-times-circle" /></span>
        </template>
      </Tablelist>
    </template>
  </MainSlot>

  <ModelDialog
          v-model="dialogVisible"
          @close="handleClose"
          @cancel="handleCancel"
          :title="modalTitle"
          :mdlActionList="[
      {key:'confirm',label:t('common.ok'),type:'primary'}
    ]"
          @mdlActionFn="handleConfirm"
  >
    <Form
            ref="formRuleRef"
            :formData="productForm"
            :formList="productFormList"
            :labelPosition="true"
            :rules="formRules"
            @formDataSelect="formDataSelect"></Form>
  </ModelDialog>


  <!-- 更新日志服务器配置弹窗 -->
  <ModelDialog
    v-model="logServerVisible"
    @close="handleLogServerClose"
    @cancel="handleLogServerCancel"
    :title="logservermodalTitle"
    :mdlActionList="[
      {key:'confirm',label:t('common.ok'),type:'primary'}
    ]"
    @mdlActionFn="handleLogServerConfirm"
  >
    <div class="log-server-container">
      <!-- 日志服务器配置表单 -->
      <Form
        ref="logServerFormRef"
        :formData="logServerForm"
        :formList="logServerFormList"
        :labelPosition="true"
        :rules="logServerRules"
      ></Form>
      
      <!-- 添加新配置按钮 -->
      <div class="add-server-btn">
        <el-button type="primary" @click="addLogServer">
          {{ t('form.addServer') }}
        </el-button>
      </div>
      
      <!-- 日志服务器配置列表 -->
      <div class="log-server-list" v-if="logServerList.length > 0">
        <h3>{{ t('form.configuredServers') }}</h3>
        <el-table :data="logServerList" style="width: 100%">
          <el-table-column prop="port" :label="t('form.port')" width="120"></el-table-column>
          <el-table-column prop="protocol" :label="t('form.protocol')" width="120"></el-table-column>
          <el-table-column :label="t('form.status')" width="100">
            <template #default="{row}">
              <span v-if="row.enabled" style="color: #009688;font-size: 14px;">
                <font-awesome-icon icon="far fa-check-circle" />
              </span>
              <span v-else style="color: #c1000b;font-size: 14px;">
                <font-awesome-icon icon="far fa-times-circle" />
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="create_time" :label="t('form.createTime')" width="180"></el-table-column>
          <el-table-column prop="update_time" :label="t('form.updateTime')" width="180"></el-table-column>
          <el-table-column :label="t('form.operations')">
            <template #default="{row}">
              <el-button type="primary" size="small" @click="editLogServer(row)">
                {{ t('form.edit') }}
              </el-button>
              <el-button type="danger" size="small" @click="deleteLogServer(row.id)">
                {{ t('form.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="logServerPagination.current_page"
            v-model:page-size="logServerPagination.page_size"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="logServerPagination.total"
            @size-change="handleLogServerSizeChange"
            @current-change="handleLogServerPageChange"
          />
        </div>
      </div>
      
      <!-- 无数据提示 -->
      <el-empty v-else description="暂无配置数据" />
    </div>
  </ModelDialog>
</template>


<style lang="less" scoped>
.log-server-container {
  padding: 10px;
  
  .log-server-list {
    margin-top: 20px;
    border-top: 1px solid #eee;
    padding-top: 15px;
    
    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      color: #333;
    }
  }
  
  .add-server-btn {
    margin-top: 15px;
    margin-bottom: 15px;
    text-align: left;
  }
  
  .pagination-container {
    margin-top: 15px;
    text-align: right;
  }
}
</style>